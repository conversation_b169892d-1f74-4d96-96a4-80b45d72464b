"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>onte<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Icon<PERSON>utton,
} from "@mui/material";
import {
  SchoolIcon,
  EditIcon,
  DeleteIcon,
  InfoIcon,
  CheckIcon,
} from "@/components/icons";

// Simple icon components using SVG
const PersonIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
  </svg>
);

const GroupIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A2.996 2.996 0 0 0 17.06 7H16.94c-1.05 0-1.99.54-2.54 1.37L12 16h2.5v6h5.5zm-12.5 0v-6H10l-2.54-7.63A2.996 2.996 0 0 0 4.56 7H3.44c-1.05 0-1.99.54-2.54 1.37L-1.5 16H1v6h6.5z" />
  </svg>
);

const SupervisorIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9M15 11.5C15.8 11.5 16.5 12.2 16.5 13S15.8 14.5 15 14.5 13.5 13.8 13.5 13 14.2 11.5 15 11.5M5 11.5C5.8 11.5 6.5 12.2 6.5 13S5.8 14.5 5 14.5 3.5 13.8 3.5 13 4.2 11.5 5 11.5M12 7.5C12.8 7.5 13.5 8.2 13.5 9S12.8 10.5 12 10.5 10.5 9.8 10.5 9 11.2 7.5 12 7.5M12 12C13.66 12 15 13.34 15 15V16H9V15C9 13.34 10.34 12 12 12Z" />
  </svg>
);

const DocumentIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
  </svg>
);

const AssignmentIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M19,3H14.82C14.4,1.84 13.3,1 12,1C10.7,1 9.6,1.84 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3" />
  </svg>
);

const LinkIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z" />
  </svg>
);

const GridViewIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3,11H11V3H3M3,21H11V13H3M13,21H21V13H13M13,3V11H21V3" />
  </svg>
);

const ListViewIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M3,5H21V3H3V5M3,13H21V11H3V13M3,21H21V19H3V21Z" />
  </svg>
);
import { AppLink } from "@/components/common";

// Interface for menu item data
interface MenuItemData {
  id: string;
  title: string;
  icon: React.ReactNode;
  href: string;
}

// Interface for menu section data
interface MenuSectionData {
  id: string;
  title: string;
  items: MenuItemData[];
}

// Props interface for the Menu component
interface MenuProps {
  title?: string;
  sections?: MenuSectionData[];
  viewMode?: "grid" | "list";
  onViewModeChange?: (mode: "grid" | "list") => void;
}

// Default menu data based on the image
const defaultSections: MenuSectionData[] = [
  {
    id: "user-management",
    title: "11.1 Quản lý người dùng",
    items: [
      {
        id: "user-roles",
        title: "11.1.1 Quản lý tài khoản",
        icon: <PersonIcon />,
        href: "/he-thong/quan-ly-tai-khoan",
      },
      {
        id: "user-permissions",
        title: "11.1.2 Quản lý nhóm người dùng",
        icon: <GroupIcon />,
        href: "/he-thong/quan-ly-nhom-nguoi-dung",
      },
      {
        id: "user-groups",
        title: "11.1.3 Phân quyền nhóm người dùng",
        icon: <SupervisorIcon />,
        href: "/he-thong/phan-quyen-nhom-nguoi-dung",
      },
    ],
  },
  {
    id: "school-info",
    title: "11.2 Thông tin nhà trường",
    items: [
      {
        id: "school-info-detail",
        title: "11.2.1 Thông tin nhà trường",
        icon: <SchoolIcon />,
        href: "/he-thong/thong-tin-nha-truong",
      },
      {
        id: "class-management",
        title: "11.2.2 Cấu hình năm học",
        icon: <GroupIcon />,
        href: "/he-thong/cau-hinh-nam-hoc",
      },
      {
        id: "subject-management",
        title: "11.2.3 Cấu hình chủ kỳ",
        icon: <AssignmentIcon />,
        href: "/he-thong/cau-hinh-chu-ky",
      },
      {
        id: "grade-management",
        title: "11.2.4 Quản lý năm in",
        icon: <GroupIcon />,
        href: "/he-thong/quan-ly-nam-in",
      },
      {
        id: "budget-category",
        title: "11.2.5 Bảng bố cái tiêu CSDL Bộ",
        icon: <DocumentIcon />,
        href: "/he-thong/bang-bo-cai-tieu-csdl-bo",
      },
    ],
  },
  {
    id: "documents",
    title: "11.3 Trang chủ ban đọc",
    items: [
      {
        id: "banner-management",
        title: "11.3.1 Quản lý ảnh bìa (banner)",
        icon: <DocumentIcon />,
        href: "/he-thong/quan-ly-anh-bia",
      },
      {
        id: "news-management",
        title: "11.3.2 Quản lý bình luận",
        icon: <DocumentIcon />,
        href: "/he-thong/quan-ly-binh-luan",
      },
      {
        id: "website-links",
        title: "11.3.3 Liên kết website",
        icon: <LinkIcon />,
        href: "/he-thong/lien-ket-website",
      },
    ],
  },
];

// Menu Card Component
const MenuCard: React.FC<{ item: MenuItemData }> = ({ item }) => {
  return (
    <Card
      sx={{
        height: "100%",
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          transform: "translateY(-2px)",
          boxShadow: 3,
        },
        border: "1px solid",
        borderColor: "grey.200",
      }}
    >
      <CardContent sx={{ p: 2.5, "&:last-child": { pb: 2.5 } }}>
        <Stack spacing={2} alignItems="flex-start">
          {/* Icon */}
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: "50%",
              backgroundColor: "primary.main",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              "& svg": {
                fontSize: 24,
              },
            }}
          >
            {item.icon}
          </Box>

          {/* Title */}
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              color: "text.primary",
              lineHeight: 1.4,
              minHeight: "2.8em", // Ensure consistent height
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
            }}
          >
            {item.title}
          </Typography>

          {/* Link */}
          <AppLink
            href={item.href}
            sx={{
              color: "primary.main",
              textDecoration: "none",
              fontSize: "0.875rem",
              fontWeight: 500,
              "&:hover": {
                textDecoration: "underline",
              },
            }}
          >
            Xem &gt;
          </AppLink>
        </Stack>
      </CardContent>
    </Card>
  );
};

// Main Menu Component
const Menu: React.FC<MenuProps> = ({
  title = "Hệ thống",
  sections = defaultSections,
  viewMode = "grid",
  onViewModeChange,
}) => {
  const handleViewModeChange = (mode: "grid" | "list") => {
    onViewModeChange?.(mode);
  };

  return (
    <Box
      sx={{ p: 3, backgroundColor: "background.default", minHeight: "100vh" }}
    >
      {/* Header */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography
          variant="h6"
          sx={{ fontWeight: 600, color: "text.primary" }}
        >
          {title}
        </Typography>

        {/* View Mode Toggle */}
        <Stack direction="row" spacing={0.5}>
          <IconButton
            size="small"
            onClick={() => handleViewModeChange("grid")}
            sx={{
              color: viewMode === "grid" ? "primary.main" : "text.secondary",
              backgroundColor:
                viewMode === "grid" ? "primary.light" : "transparent",
            }}
          >
            <GridViewIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleViewModeChange("list")}
            sx={{
              color: viewMode === "list" ? "primary.main" : "text.secondary",
              backgroundColor:
                viewMode === "list" ? "primary.light" : "transparent",
            }}
          >
            <ListViewIcon fontSize="small" />
          </IconButton>
        </Stack>
      </Stack>

      {/* Menu Sections */}
      <Stack spacing={4}>
        {sections.map((section) => (
          <Box key={section.id}>
            {/* Section Title */}
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: "text.primary",
                mb: 2,
              }}
            >
              {section.title}
            </Typography>

            {/* Section Items */}
            <Grid container spacing={2}>
              {section.items.map((item) => (
                <Grid key={item.id} xs={12} sm={6} md={4} lg={3} xl={2.4}>
                  <MenuCard item={item} />
                </Grid>
              ))}
            </Grid>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default Menu;
export type { MenuProps, MenuSectionData, MenuItemData };
